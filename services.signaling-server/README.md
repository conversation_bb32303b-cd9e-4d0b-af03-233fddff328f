# Signaling Server - WebRTC Signaling Service

The Signaling Server handles WebRTC connection setup (SDP/ICE negotiation) between clients and the Voice Gateway SFU. It manages the signaling plane while the Voice Gateway handles the media plane, providing a clean separation of concerns in the Cortexa platform architecture.

## Features

- **WebRTC Signaling**: SDP offer/answer and ICE candidate exchange
- **Authentication**: JWT token validation and gateway header support
- **Session Management**: Call and participant state tracking
- **Real-time Communication**: WebSocket-based signaling with Socket.IO
- **Monitoring**: Prometheus metrics and health checks
- **Security**: Token-based authentication and CORS protection
- **Scalable Architecture**: Stateless design for horizontal scaling

## Architecture

The service acts as a signaling intermediary between WebRTC clients and the Voice Gateway SFU:

- **SignalingManager**: Handles WebRTC signaling protocol
- **AuthenticationManager**: JWT and gateway header authentication
- **MetricsServer**: Prometheus-compatible metrics
- **Socket.IO Integration**: Real-time bidirectional communication

## Configuration

Configure via environment variables. See `.env.example` for all options:

### Key Configuration Options

- `JWT_SECRET`: Secret for JWT token validation
- `VOICE_GATEWAY_URL`: URL of the Voice Gateway SFU service
- `ENABLE_AUTH`: Enable/disable authentication (default: true)
- `WS_HEARTBEAT_INTERVAL`: WebSocket heartbeat interval
- `MAX_CONNECTIONS`: Maximum concurrent WebSocket connections

## API

### WebSocket Events (Socket.IO)

#### Client to Server

- `join-call`: Join a call with participant details
- `offer`: Send SDP offer for WebRTC negotiation
- `answer`: Send SDP answer for WebRTC negotiation
- `ice-candidate`: Send ICE candidate for connectivity
- `leave-call`: Leave the current call

#### Server to Client

- `offer`: Relay SDP offer from another participant
- `answer`: Relay SDP answer from another participant
- `ice-candidate`: Relay ICE candidate from another participant
- `participant-left`: Notification when participant leaves

### HTTP Endpoints

- `GET /health`: Health check with service status
- `GET /ready`: Readiness probe for load balancers
- `GET /metrics`: Prometheus metrics (on port 9091)

## Authentication

The service supports two authentication methods:

### JWT Token Authentication

```javascript
const socket = io('ws://localhost:8001', {
  auth: {
    token: 'your-jwt-token'
  }
});
```

### Gateway Header Authentication

For requests proxied through the API gateway:
- `X-User-Id`: User identifier
- `X-User-Email`: User email
- `X-Token-Audience`: Token audience
- `X-Token-Issuer`: Token issuer
- `X-Token-Expires-At`: Token expiration timestamp
- `X-Token-Issued-At`: Token issued timestamp

## Development

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

```bash
npm install
cp .env.example .env
# Edit .env with your configuration
```

### Running

```bash
# Development mode
npm run dev

# Production mode
npm start
```

## WebRTC Signaling Flow

1. **Join Call**: Client connects and joins a call
2. **Offer Exchange**: Participants exchange SDP offers
3. **Answer Exchange**: Participants respond with SDP answers
4. **ICE Candidates**: Connectivity information is exchanged
5. **Media Connection**: Direct media connection established with SFU
6. **Leave Call**: Participant leaves and cleanup occurs

## Monitoring

### Prometheus Metrics

Available at `http://localhost:9091/metrics`:

- `signaling_connections_total`: Total WebSocket connections
- `signaling_connections_active`: Currently active connections
- `signaling_sessions_total`: Total signaling sessions
- `signaling_calls_total`: Total calls handled
- `signaling_offers_total`: Total SDP offers processed
- `signaling_answers_total`: Total SDP answers processed
- `signaling_ice_candidates_total`: Total ICE candidates processed
- `signaling_errors_total`: Error counts by type

### Health Checks

- `GET /health`: Detailed service health information
- `GET /ready`: Simple readiness check

## Security

- JWT token validation for WebSocket connections
- CORS configuration for cross-origin requests
- Helmet.js for HTTP security headers
- Rate limiting support (configurable)
- Non-root Docker container execution

## Integration with Voice Gateway

The Signaling Server coordinates with the Voice Gateway SFU:

1. **Call Setup**: Signaling server manages participant joining
2. **WebRTC Negotiation**: SDP/ICE exchange through signaling
3. **Media Transport**: Direct connection to Voice Gateway SFU
4. **State Synchronization**: Participant state shared between services

## Error Handling

The service provides comprehensive error handling:

- Authentication failures with specific error messages
- WebRTC signaling errors with context
- Connection timeouts and cleanup
- Graceful degradation for external service failures

## Performance

Designed for high-performance signaling:

- Stateless architecture for horizontal scaling
- Efficient WebSocket connection management
- Minimal memory footprint per connection
- Fast SDP/ICE message relay

## Troubleshooting

### Common Issues

1. **Authentication failures**: Check JWT secret and token format
2. **Connection timeouts**: Verify WebSocket configuration
3. **Voice Gateway connectivity**: Check service URL and health
4. **CORS errors**: Configure allowed origins properly

### Debug Logging

Enable debug logging: `LOG_LEVEL=debug`

## Contributing

1. Follow existing code patterns and style
2. Add tests for new functionality
3. Update documentation for API changes
4. Ensure all linting passes

## License

MIT License - see LICENSE file for details.

# Signaling Server - WebRTC Signaling Service
FROM node:18-alpine AS builder

RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

FROM node:18-alpine AS production

RUN apk add --no-cache \
    dumb-init \
    && addgroup -g 1001 -S nodejs \
    && adduser -S signaling -u 1001

WORKDIR /app

COPY --from=builder --chown=signaling:nodejs /app/node_modules ./node_modules
COPY --chown=signaling:nodejs src/ ./src/
COPY --chown=signaling:nodejs package*.json ./

RUN mkdir -p logs && chown signaling:nodejs logs

USER signaling

EXPOSE 8001 9091

HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:8001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"

ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "src/index.js"]

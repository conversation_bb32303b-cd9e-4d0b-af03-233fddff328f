/**
 * Metrics Server for Signaling Service
 */

const express = require('express');
const http = require('http');
const config = require('../config');
const logger = require('../utils/logger');

class MetricsServer {
  constructor() {
    this.app = express();
    this.server = null;
    this.metrics = {
      // Connection metrics
      totalConnections: 0,
      activeConnections: 0,
      
      // Session metrics
      totalSessions: 0,
      activeSessions: 0,
      
      // Call metrics
      totalCalls: 0,
      activeCalls: 0,
      
      // Signaling metrics
      totalOffers: 0,
      totalAnswers: 0,
      totalIceCandidates: 0,
      
      // Error metrics
      totalErrors: 0,
      authErrors: 0,
      signalingErrors: 0
    };
    
    this.setupRoutes();
  }

  setupRoutes() {
    this.app.get('/metrics', (req, res) => {
      const prometheusMetrics = this.generatePrometheusMetrics();
      res.set('Content-Type', 'text/plain');
      res.send(prometheusMetrics);
    });

    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'signaling-server-metrics'
      });
    });
  }

  generatePrometheusMetrics() {
    const timestamp = Date.now();
    const memUsage = process.memoryUsage();
    
    return `
# HELP signaling_connections_total Total number of WebSocket connections
# TYPE signaling_connections_total counter
signaling_connections_total ${this.metrics.totalConnections} ${timestamp}

# HELP signaling_connections_active Number of currently active connections
# TYPE signaling_connections_active gauge
signaling_connections_active ${this.metrics.activeConnections} ${timestamp}

# HELP signaling_sessions_total Total number of signaling sessions created
# TYPE signaling_sessions_total counter
signaling_sessions_total ${this.metrics.totalSessions} ${timestamp}

# HELP signaling_sessions_active Number of currently active sessions
# TYPE signaling_sessions_active gauge
signaling_sessions_active ${this.metrics.activeSessions} ${timestamp}

# HELP signaling_calls_total Total number of calls handled
# TYPE signaling_calls_total counter
signaling_calls_total ${this.metrics.totalCalls} ${timestamp}

# HELP signaling_calls_active Number of currently active calls
# TYPE signaling_calls_active gauge
signaling_calls_active ${this.metrics.activeCalls} ${timestamp}

# HELP signaling_offers_total Total number of SDP offers processed
# TYPE signaling_offers_total counter
signaling_offers_total ${this.metrics.totalOffers} ${timestamp}

# HELP signaling_answers_total Total number of SDP answers processed
# TYPE signaling_answers_total counter
signaling_answers_total ${this.metrics.totalAnswers} ${timestamp}

# HELP signaling_ice_candidates_total Total number of ICE candidates processed
# TYPE signaling_ice_candidates_total counter
signaling_ice_candidates_total ${this.metrics.totalIceCandidates} ${timestamp}

# HELP signaling_memory_usage_bytes Memory usage in bytes
# TYPE signaling_memory_usage_bytes gauge
signaling_memory_usage_bytes{type="rss"} ${memUsage.rss} ${timestamp}
signaling_memory_usage_bytes{type="heap_total"} ${memUsage.heapTotal} ${timestamp}
signaling_memory_usage_bytes{type="heap_used"} ${memUsage.heapUsed} ${timestamp}
signaling_memory_usage_bytes{type="external"} ${memUsage.external} ${timestamp}

# HELP signaling_errors_total Total number of errors
# TYPE signaling_errors_total counter
signaling_errors_total{type="total"} ${this.metrics.totalErrors} ${timestamp}
signaling_errors_total{type="auth"} ${this.metrics.authErrors} ${timestamp}
signaling_errors_total{type="signaling"} ${this.metrics.signalingErrors} ${timestamp}

# HELP signaling_uptime_seconds Service uptime in seconds
# TYPE signaling_uptime_seconds gauge
signaling_uptime_seconds ${process.uptime()} ${timestamp}
`.trim();
  }

  // Metric update methods
  incrementConnectionsTotal() {
    this.metrics.totalConnections++;
  }

  setActiveConnections(count) {
    this.metrics.activeConnections = count;
  }

  incrementSessionsTotal() {
    this.metrics.totalSessions++;
  }

  setActiveSessions(count) {
    this.metrics.activeSessions = count;
  }

  incrementCallsTotal() {
    this.metrics.totalCalls++;
  }

  setActiveCalls(count) {
    this.metrics.activeCalls = count;
  }

  incrementOffersTotal() {
    this.metrics.totalOffers++;
  }

  incrementAnswersTotal() {
    this.metrics.totalAnswers++;
  }

  incrementIceCandidatesTotal() {
    this.metrics.totalIceCandidates++;
  }

  incrementErrorsTotal() {
    this.metrics.totalErrors++;
  }

  incrementAuthErrors() {
    this.metrics.authErrors++;
  }

  incrementSignalingErrors() {
    this.metrics.signalingErrors++;
  }

  updateMetricsFromStats(stats) {
    this.setActiveSessions(stats.totalSessions);
    this.setActiveCalls(stats.totalCalls);
  }

  async start() {
    return new Promise((resolve, reject) => {
      this.server = http.createServer(this.app);
      
      this.server.listen(config.monitoring.metricsPort, (err) => {
        if (err) {
          logger.error('Failed to start metrics server', { 
            error: err.message,
            port: config.monitoring.metricsPort
          });
          reject(err);
        } else {
          logger.info('Metrics server started', {
            port: config.monitoring.metricsPort
          });
          resolve();
        }
      });

      this.server.on('error', (err) => {
        logger.error('Metrics server error', { error: err.message });
      });
    });
  }

  async stop() {
    if (this.server) {
      return new Promise((resolve) => {
        this.server.close(() => {
          logger.info('Metrics server stopped');
          resolve();
        });
      });
    }
  }

  getMetrics() {
    return { ...this.metrics };
  }
}

module.exports = MetricsServer;

/**
 * Configuration module for Signaling Service
 */

require('dotenv').config();
const Joi = require('joi');

const configSchema = Joi.object({
  service: Joi.object({
    name: Joi.string().default('signaling-server'),
    environment: Joi.string().valid('development', 'production', 'test').default('development'),
    port: Joi.number().port().default(8001),
    host: Joi.string().default('0.0.0.0')
  }).required(),

  logging: Joi.object({
    level: Joi.string().valid('error', 'warn', 'info', 'debug').default('info'),
    format: Joi.string().valid('json', 'simple').default('json')
  }).required(),

  auth: Joi.object({
    enableAuth: Joi.boolean().default(true),
    jwtSecret: Joi.string().required(),
    jwtExpiresIn: Joi.string().default('1h'),
    headerPrefix: Joi.string().default('Bearer')
  }).required(),

  cors: Joi.object({
    origin: Joi.alternatives().try(
      Joi.string(),
      Joi.array().items(Joi.string()),
      Joi.boolean()
    ).default('*')
  }).required(),

  websocket: Joi.object({
    heartbeatInterval: Joi.number().min(1000).default(30000),
    connectionTimeout: Joi.number().min(1000).default(60000),
    maxConnections: Joi.number().min(1).default(1000)
  }).required(),

  rateLimiting: Joi.object({
    enabled: Joi.boolean().default(true),
    windowMs: Joi.number().min(1000).default(60000),
    maxRequests: Joi.number().min(1).default(100)
  }).required(),

  monitoring: Joi.object({
    enableMetrics: Joi.boolean().default(true),
    metricsPort: Joi.number().port().default(9091)
  }).required(),

  externalServices: Joi.object({
    voiceGatewayUrl: Joi.string().uri().default('http://localhost:8002'),
    callControlServiceUrl: Joi.string().uri().default('http://localhost:8003')
  }).required(),

  limits: Joi.object({
    maxConcurrentCalls: Joi.number().min(1).default(100),
    maxParticipantsPerCall: Joi.number().min(2).default(10)
  }).required(),

  kafka: Joi.object({
    bootstrapServers: Joi.string().default('localhost:9092'),
    clientId: Joi.string().default('signaling-server'),
    enableEvents: Joi.boolean().default(true)
  }).required()
});

const rawConfig = {
  service: {
    name: process.env.SERVICE_NAME,
    environment: process.env.NODE_ENV,
    port: parseInt(process.env.PORT, 10),
    host: process.env.HOST
  },

  logging: {
    level: process.env.LOG_LEVEL,
    format: process.env.LOG_FORMAT
  },

  auth: {
    enableAuth: process.env.ENABLE_AUTH !== 'false',
    jwtSecret: process.env.JWT_SECRET,
    jwtExpiresIn: process.env.JWT_EXPIRES_IN,
    headerPrefix: process.env.AUTH_HEADER_PREFIX
  },

  cors: {
    origin: process.env.CORS_ORIGIN === '*' ? '*' : 
           process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',').map(origin => origin.trim()) :
           undefined
  },

  websocket: {
    heartbeatInterval: parseInt(process.env.WS_HEARTBEAT_INTERVAL, 10),
    connectionTimeout: parseInt(process.env.WS_CONNECTION_TIMEOUT, 10),
    maxConnections: parseInt(process.env.MAX_CONNECTIONS, 10)
  },

  rateLimiting: {
    enabled: process.env.ENABLE_RATE_LIMITING !== 'false',
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS, 10),
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS, 10)
  },

  monitoring: {
    enableMetrics: process.env.ENABLE_METRICS !== 'false',
    metricsPort: parseInt(process.env.METRICS_PORT, 10)
  },

  externalServices: {
    voiceGatewayUrl: process.env.VOICE_GATEWAY_URL,
    callControlServiceUrl: process.env.CALL_CONTROL_SERVICE_URL
  },

  limits: {
    maxConcurrentCalls: parseInt(process.env.MAX_CONCURRENT_CALLS, 10),
    maxParticipantsPerCall: parseInt(process.env.MAX_PARTICIPANTS_PER_CALL, 10)
  },

  kafka: {
    bootstrapServers: process.env.KAFKA_BOOTSTRAP_SERVERS,
    clientId: process.env.KAFKA_CLIENT_ID,
    enableEvents: process.env.KAFKA_ENABLE_EVENTS !== 'false'
  }
};

const { error, value: config } = configSchema.validate(rawConfig, {
  allowUnknown: false,
  stripUnknown: true
});

if (error) {
  console.error('Configuration validation error:', error.details);
  process.exit(1);
}

module.exports = config;

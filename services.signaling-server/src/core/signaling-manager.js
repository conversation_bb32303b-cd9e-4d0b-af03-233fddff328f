/**
 * Signaling Manager for Signaling Service
 * 
 * Manages WebRTC signaling between clients and the Voice Gateway SFU.
 * Handles SDP offer/answer exchange, ICE candidate relay, and call state management.
 */

const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const config = require('../config');
const logger = require('../utils/logger');

class SignalingManager {
  constructor() {
    this.sessions = new Map(); // socketId -> session
    this.calls = new Map(); // callId -> call info
    this.voiceGatewayClient = axios.create({
      baseURL: config.externalServices.voiceGatewayUrl,
      timeout: 10000
    });
    this.isInitialized = false;
  }

  async initialize() {
    try {
      logger.info('Initializing Signaling Manager...');

      // Test connection to Voice Gateway
      await this.testVoiceGatewayConnection();

      this.isInitialized = true;
      logger.info('Signaling Manager initialized successfully');

    } catch (error) {
      logger.error('Failed to initialize Signaling Manager', { error: error.message });
      throw error;
    }
  }

  async testVoiceGatewayConnection() {
    try {
      const response = await this.voiceGatewayClient.get('/health');
      logger.info('Voice Gateway connection test successful', {
        status: response.status,
        voiceGatewayUrl: config.externalServices.voiceGatewayUrl
      });
    } catch (error) {
      logger.warn('Voice Gateway connection test failed', {
        error: error.message,
        voiceGatewayUrl: config.externalServices.voiceGatewayUrl
      });
      // Don't throw here - the service might not be ready yet
    }
  }

  async handleJoinCall(socket, data) {
    const { callId, participantId, rtpCapabilities } = data;
    
    logger.logCallEvent(callId, 'signaling-join-request', {
      socketId: socket.id,
      participantId,
      userId: socket.user?.id
    });

    try {
      // Validate input
      if (!callId || !participantId) {
        throw new Error('Missing required parameters: callId, participantId');
      }

      // Check if user already has a session
      if (this.sessions.has(socket.id)) {
        throw new Error('Socket already has an active session');
      }

      // Create session
      const session = {
        socketId: socket.id,
        callId,
        participantId,
        userId: socket.user?.id,
        rtpCapabilities,
        state: 'joining',
        createdAt: new Date(),
        lastActivity: new Date()
      };

      this.sessions.set(socket.id, session);

      // Update call info
      let call = this.calls.get(callId);
      if (!call) {
        call = {
          id: callId,
          participants: new Map(),
          createdAt: new Date(),
          lastActivity: new Date()
        };
        this.calls.set(callId, call);
      }

      call.participants.set(participantId, {
        socketId: socket.id,
        participantId,
        userId: socket.user?.id,
        joinedAt: new Date()
      });

      // Update session state
      session.state = 'joined';
      session.lastActivity = new Date();

      logger.logCallEvent(callId, 'signaling-join-success', {
        socketId: socket.id,
        participantId,
        totalParticipants: call.participants.size
      });

      return {
        sessionId: socket.id,
        callId,
        participantId,
        totalParticipants: call.participants.size
      };

    } catch (error) {
      logger.error('Failed to handle join call', {
        callId,
        socketId: socket.id,
        error: error.message
      });
      throw error;
    }
  }

  async handleOffer(socket, data) {
    const { sdp, type, targetParticipantId } = data;
    const session = this.sessions.get(socket.id);

    if (!session) {
      throw new Error('No active session found');
    }

    logger.logSignalingEvent(socket.id, 'offer-received', {
      callId: session.callId,
      participantId: session.participantId,
      targetParticipantId,
      sdpType: type
    });

    try {
      // Update session activity
      session.lastActivity = new Date();

      if (targetParticipantId) {
        // Relay offer to specific participant
        await this.relayToParticipant(session.callId, targetParticipantId, 'offer', {
          sdp,
          type,
          fromParticipantId: session.participantId
        });
      } else {
        // Broadcast offer to all other participants in the call
        await this.broadcastToCall(session.callId, session.participantId, 'offer', {
          sdp,
          type,
          fromParticipantId: session.participantId
        });
      }

      logger.logSignalingEvent(socket.id, 'offer-relayed', {
        callId: session.callId,
        targetParticipantId
      });

      return { success: true };

    } catch (error) {
      logger.error('Failed to handle offer', {
        socketId: socket.id,
        callId: session.callId,
        error: error.message
      });
      throw error;
    }
  }

  async handleAnswer(socket, data) {
    const { sdp, type, targetParticipantId } = data;
    const session = this.sessions.get(socket.id);

    if (!session) {
      throw new Error('No active session found');
    }

    logger.logSignalingEvent(socket.id, 'answer-received', {
      callId: session.callId,
      participantId: session.participantId,
      targetParticipantId,
      sdpType: type
    });

    try {
      // Update session activity
      session.lastActivity = new Date();

      // Relay answer to target participant
      await this.relayToParticipant(session.callId, targetParticipantId, 'answer', {
        sdp,
        type,
        fromParticipantId: session.participantId
      });

      logger.logSignalingEvent(socket.id, 'answer-relayed', {
        callId: session.callId,
        targetParticipantId
      });

      return { success: true };

    } catch (error) {
      logger.error('Failed to handle answer', {
        socketId: socket.id,
        callId: session.callId,
        error: error.message
      });
      throw error;
    }
  }

  async handleIceCandidate(socket, data) {
    const { candidate, sdpMLineIndex, sdpMid, targetParticipantId } = data;
    const session = this.sessions.get(socket.id);

    if (!session) {
      throw new Error('No active session found');
    }

    logger.logSignalingEvent(socket.id, 'ice-candidate-received', {
      callId: session.callId,
      participantId: session.participantId,
      targetParticipantId
    });

    try {
      // Update session activity
      session.lastActivity = new Date();

      if (targetParticipantId) {
        // Relay to specific participant
        await this.relayToParticipant(session.callId, targetParticipantId, 'ice-candidate', {
          candidate,
          sdpMLineIndex,
          sdpMid,
          fromParticipantId: session.participantId
        });
      } else {
        // Broadcast to all other participants
        await this.broadcastToCall(session.callId, session.participantId, 'ice-candidate', {
          candidate,
          sdpMLineIndex,
          sdpMid,
          fromParticipantId: session.participantId
        });
      }

      return { success: true };

    } catch (error) {
      logger.error('Failed to handle ICE candidate', {
        socketId: socket.id,
        callId: session.callId,
        error: error.message
      });
      throw error;
    }
  }

  async handleLeaveCall(socket, data) {
    const session = this.sessions.get(socket.id);

    if (!session) {
      return; // No active session
    }

    logger.logCallEvent(session.callId, 'signaling-leave-request', {
      socketId: socket.id,
      participantId: session.participantId
    });

    try {
      await this.cleanupSession(socket.id);

      logger.logCallEvent(session.callId, 'signaling-leave-success', {
        socketId: socket.id,
        participantId: session.participantId
      });

    } catch (error) {
      logger.error('Failed to handle leave call', {
        socketId: socket.id,
        callId: session.callId,
        error: error.message
      });
      throw error;
    }
  }

  async relayToParticipant(callId, targetParticipantId, event, data) {
    const call = this.calls.get(callId);
    if (!call) {
      throw new Error('Call not found');
    }

    const participant = call.participants.get(targetParticipantId);
    if (!participant) {
      throw new Error('Target participant not found');
    }

    // In a real implementation, you'd need access to the Socket.IO server
    // to emit to specific sockets. For now, this is a placeholder.
    logger.logSignalingEvent(participant.socketId, 'relay-to-participant', {
      event,
      callId,
      targetParticipantId,
      dataKeys: Object.keys(data)
    });

    // TODO: Implement actual socket emission
    // socket.to(participant.socketId).emit(event, data);
  }

  async broadcastToCall(callId, excludeParticipantId, event, data) {
    const call = this.calls.get(callId);
    if (!call) {
      throw new Error('Call not found');
    }

    for (const [participantId, participant] of call.participants) {
      if (participantId !== excludeParticipantId) {
        // TODO: Implement actual socket emission
        logger.logSignalingEvent(participant.socketId, 'broadcast-to-call', {
          event,
          callId,
          participantId,
          dataKeys: Object.keys(data)
        });
      }
    }
  }

  async handleDisconnection(socket) {
    const session = this.sessions.get(socket.id);
    if (!session) return;

    logger.logCallEvent(session.callId, 'signaling-disconnection', {
      socketId: socket.id,
      participantId: session.participantId
    });

    try {
      await this.cleanupSession(socket.id);
    } catch (error) {
      logger.error('Error during signaling disconnection cleanup', {
        socketId: socket.id,
        error: error.message
      });
    }
  }

  async cleanupSession(socketId) {
    const session = this.sessions.get(socketId);
    if (!session) return;

    try {
      // Remove participant from call
      const call = this.calls.get(session.callId);
      if (call) {
        call.participants.delete(session.participantId);

        // Notify other participants about departure
        await this.broadcastToCall(session.callId, session.participantId, 'participant-left', {
          participantId: session.participantId
        });

        // If no participants left, remove the call
        if (call.participants.size === 0) {
          this.calls.delete(session.callId);
          logger.logCallEvent(session.callId, 'signaling-call-ended', {
            reason: 'no-participants'
          });
        }
      }

      // Remove session
      this.sessions.delete(socketId);

      logger.logCallEvent(session.callId, 'signaling-session-cleaned', {
        socketId,
        participantId: session.participantId
      });

    } catch (error) {
      logger.error('Error during session cleanup', {
        socketId,
        callId: session.callId,
        error: error.message
      });
      throw error;
    }
  }

  isReady() {
    return this.isInitialized;
  }

  getStatus() {
    return {
      initialized: this.isInitialized,
      activeSessions: this.sessions.size,
      activeCalls: this.calls.size,
      voiceGatewayUrl: config.externalServices.voiceGatewayUrl
    };
  }

  getStats() {
    return {
      totalSessions: this.sessions.size,
      totalCalls: this.calls.size,
      sessions: Array.from(this.sessions.values()).map(session => ({
        socketId: session.socketId,
        callId: session.callId,
        participantId: session.participantId,
        state: session.state,
        createdAt: session.createdAt,
        lastActivity: session.lastActivity
      })),
      calls: Array.from(this.calls.values()).map(call => ({
        id: call.id,
        participantCount: call.participants.size,
        createdAt: call.createdAt,
        lastActivity: call.lastActivity
      }))
    };
  }

  async cleanup() {
    logger.info('Cleaning up Signaling Manager...');

    try {
      // Clean up all sessions
      for (const socketId of this.sessions.keys()) {
        await this.cleanupSession(socketId);
      }

      // Clear all maps
      this.sessions.clear();
      this.calls.clear();

      this.isInitialized = false;
      logger.info('Signaling Manager cleanup completed');

    } catch (error) {
      logger.error('Error during Signaling Manager cleanup', {
        error: error.message
      });
      throw error;
    }
  }
}

module.exports = SignalingManager;

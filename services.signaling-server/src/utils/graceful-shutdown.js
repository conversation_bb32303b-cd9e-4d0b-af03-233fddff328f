/**
 * Graceful shutdown utility for Signaling Service
 */

const logger = require('./logger');

let isShuttingDown = false;

function setupGracefulShutdown(server, cleanupCallback) {
  const shutdown = async (signal) => {
    if (isShuttingDown) {
      logger.warn(`Received ${signal} during shutdown, forcing exit...`);
      process.exit(1);
    }

    isShuttingDown = true;
    logger.info(`Received ${signal}, starting graceful shutdown...`);

    const forceShutdownTimeout = setTimeout(() => {
      logger.error('Graceful shutdown timeout, forcing exit...');
      process.exit(1);
    }, 30000);

    try {
      server.close(async (err) => {
        if (err) {
          logger.error('Error closing HTTP server', { error: err.message });
        } else {
          logger.info('HTTP server closed');
        }

        try {
          if (cleanupCallback && typeof cleanupCallback === 'function') {
            await cleanupCallback();
          }

          clearTimeout(forceShutdownTimeout);
          logger.info('Graceful shutdown completed');
          process.exit(0);
        } catch (cleanupError) {
          logger.error('Error during cleanup', { error: cleanupError.message });
          clearTimeout(forceShutdownTimeout);
          process.exit(1);
        }
      });
    } catch (error) {
      logger.error('Error during shutdown', { error: error.message });
      clearTimeout(forceShutdownTimeout);
      process.exit(1);
    }
  };

  process.on('SIGINT', () => shutdown('SIGINT'));
  process.on('SIGTERM', () => shutdown('SIGTERM'));

  process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception - shutting down', {
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name
      }
    });
    
    if (!isShuttingDown) {
      shutdown('UNCAUGHT_EXCEPTION');
    }
  });

  process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection - shutting down', {
      reason: reason instanceof Error ? {
        message: reason.message,
        stack: reason.stack,
        name: reason.name
      } : reason,
      promise: promise.toString()
    });
    
    if (!isShuttingDown) {
      shutdown('UNHANDLED_REJECTION');
    }
  });
}

module.exports = {
  setupGracefulShutdown
};

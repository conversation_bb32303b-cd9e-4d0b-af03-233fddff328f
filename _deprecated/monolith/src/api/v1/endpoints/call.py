from fastapi import <PERSON><PERSON>out<PERSON>, Depends, WebSocket

from cortexacommon.logging import get_logger

from src.core.websocket import WebSocket<PERSON>andler
from src.core.context import ApplicationContext, get_app_context


logger = get_logger(__name__)
router = APIRouter()


@router.websocket("/ws/call/{call_id}")
async def websocket_endpoint(
    websocket: WebSocket,
    call_id: str,
    context: ApplicationContext = Depends(get_app_context),
):
    logger.info(f"New WebSocket connection attempt for call {call_id}")
    await WebSocketHandler(context).handle_websocket(websocket, call_id)

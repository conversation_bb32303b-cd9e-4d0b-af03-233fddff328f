"""
HuggingFace Translation provider implementation.
"""

import asyncio
from cortexacommon.logging import get_logger
from typing import Optional

from .base import BaseTranslationProvider
from ...core.config import settings

logger = get_logger(__name__)


class HuggingFaceTranslationProvider(BaseTranslationProvider):
    """Translation provider using Hugging Face transformers."""
    
    def __init__(self, 
                 model: Optional[str] = None,
                 device: Optional[str] = None,
                 **kwargs):
        """
        Initialize HuggingFace Translation provider.
        
        Args:
            model: Translation model identifier
            device: Device for model (cpu, cuda)
            **kwargs: Additional configuration parameters
        """
        super().__init__(**kwargs)
        self.model = model or settings.translation_model
        self.device = device or settings.translation_device
        self._pipeline: Optional[object] = None
    
    async def initialize(self) -> None:
        """Initialize translation model."""
        try:
            from transformers import pipeline
            
            # Run model initialization in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            self._pipeline = await loop.run_in_executor(
                None,
                lambda: pipeline(
                    "translation",
                    model=self.model,
                    device=0 if self.device == "cuda" else -1,
                )
            )
            self._initialized = True
            logger.info(f"Translation model {self.model} initialized on {self.device}")
        except ImportError:
            logger.error("transformers not available")
            self._pipeline = None
            self._initialized = False
        except Exception as e:
            logger.error(f"Failed to initialize translation model: {e}")
            self._pipeline = None
            self._initialized = False
    
    async def translate(self, text: str) -> str:
        """
        Translate text to target language.
        
        Args:
            text: Text to translate
            
        Returns:
            Translated text
        """
        if not text.strip() or self._pipeline is None:
            return text
        
        try:
            # Run translation in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                lambda: self._pipeline(text, max_length=512)
            )
            
            if result and len(result) > 0:
                return result[0]['translation_text']
            
            return text
            
        except Exception as e:
            logger.error(f"Translation error: {e}")
            return text

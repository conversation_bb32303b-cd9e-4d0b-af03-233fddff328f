"""
OpenAI TTS provider implementation.
"""

from cortexacommon.logging import get_logger
from typing import Optional

from .base import BaseTTSProvider
from ...core.config import settings

logger = get_logger(__name__)


class OpenAITTSProvider(BaseTTSProvider):
    """Text-to-Speech provider using OpenAI TTS API."""
    
    def __init__(self, 
                 api_key: Optional[str] = None,
                 model: Optional[str] = None,
                 voice: Optional[str] = None,
                 **kwargs):
        """
        Initialize OpenAI TTS provider.
        
        Args:
            api_key: OpenAI API key
            model: TTS model identifier
            voice: Voice identifier
            **kwargs: Additional configuration parameters
        """
        super().__init__(**kwargs)
        self.api_key = api_key or settings.tts_api_key
        self.model = model or settings.tts_model
        self.voice = voice or settings.tts_voice
        self._client: Optional[object] = None
    
    async def initialize(self) -> None:
        """Initialize TTS client."""
        if not self.api_key:
            logger.warning("OpenAI API key not provided, TTS not configured")
            self._client = None
            self._initialized = False
            return
        
        try:
            import openai
            self._client = openai.AsyncOpenAI(api_key=self.api_key)
            self._initialized = True
            logger.info("OpenAI TTS client initialized")
        except ImportError:
            logger.error("openai package not available")
            self._client = None
            self._initialized = False
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI TTS client: {e}")
            self._client = None
            self._initialized = False
    
    async def synthesize(self, text: str) -> Optional[bytes]:
        """
        Synthesize speech from text.
        
        Args:
            text: Text to synthesize
            
        Returns:
            Audio bytes or None if synthesis fails
        """
        if not text.strip() or self._client is None:
            return None
        
        try:
            response = await self._client.audio.speech.create(
                model=self.model,
                voice=self.voice,
                input=text,
                response_format="wav",
            )
            
            return response.content
            
        except Exception as e:
            logger.error(f"TTS error: {e}")
            return None

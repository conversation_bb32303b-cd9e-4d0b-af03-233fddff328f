from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware

from cortexacommon.logging import get_logger, setup_service_logging

from src.api.v1.router import api_router
from src.core.config import settings
from src.core.context import get_app_context
from src.core.metrics import Metrics

# Setup common logging/monitoring
setup_service_logging(service_name=settings.service_name, level="INFO")
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager for startup and shutdown events."""
    # Startup
    logger.info("Starting Voice Gateway service",
                service=settings.service_name,
                port=settings.port)

    # Initialize application context
    app_context = await get_app_context()
    await app_context.initialize(app)

    logger.info("Voice Gateway service started successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Voice Gateway service")

    # Clean up application context
    app_context = await get_app_context()
    await app_context.cleanup()

    logger.info("Voice Gateway service shutdown complete")


# Create FastAPI application
app = FastAPI(
    title="Cortexa Voice Gateway Service",
    description="Real-time voice translation service for the Cortexa platform",
    version="0.1.0",
    openapi_url="/openapi.json",
    docs_url="/docs",
    lifespan=lifespan,
)

# Initialize metrics endpoint
Metrics.setup_fastapi_metrics(app)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API router
app.include_router(api_router)

if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "src.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info" if not settings.debug else "debug",
    )

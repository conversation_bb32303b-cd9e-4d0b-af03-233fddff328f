from pydantic import Field
from cortexacommon.events.schemas import BaseEvent


class CallStartedEvent(BaseEvent):
    """Event published when a voice call starts."""

    event_type: str = Field(default="call.started", description="Event type")
    call_id: str = Field(description="Unique call identifier")


class CallEndedEvent(BaseEvent):
    """Event published when a voice call ends."""

    event_type: str = Field(default="call.ended", description="Event type")
    call_id: str = Field(description="Unique call identifier")
    duration_seconds: float = Field(description="Call duration in seconds")


class TranscriptionEvent(BaseEvent):
    """Event published when a transcription is completed."""

    event_type: str = Field(default="transcription.completed", description="Event type")
    call_id: str = Field(description="Unique call identifier")
    segment_id: str = Field(description="Unique segment identifier")
    original_text: str = Field(description="Original transcribed text")
    translated_text: str | None = Field(description="Translated text")
    confidence_score: float = Field(description="Confidence score of transcription")
    processing_time_ms: int = Field(description="Time taken to process the segment in milliseconds")
    audio_duration_ms: int = Field(description="Duration of the audio segment in milliseconds")
    providers_used: dict[str, str] = Field(description="Providers used for each step of the pipeline")

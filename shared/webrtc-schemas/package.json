{"name": "@cortexa/webrtc-schemas", "version": "1.0.0", "description": "Shared WebRTC schemas and types for Cortexa platform", "main": "index.js", "types": "index.d.ts", "scripts": {"test": "jest", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "build": "tsc", "validate": "node scripts/validate-schemas.js"}, "keywords": ["webrtc", "schemas", "types", "validation", "cortexa"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"joi": "^17.13.3", "uuid": "^10.0.0"}, "devDependencies": {"jest": "^29.7.0", "eslint": "^8.57.0", "typescript": "^5.5.4", "@types/node": "^20.14.0", "@types/uuid": "^10.0.0"}, "engines": {"node": ">=18.0.0"}, "files": ["src/", "index.js", "index.d.ts"]}